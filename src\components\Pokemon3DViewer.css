.pokemon-3d-viewer {
  background: #1a1a1a;
  border: 2px solid #00ff00;
  border-radius: 12px;
  margin: 15px 0;
  padding: 15px;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  max-width: 100%;
  box-shadow: 0 0 20px rgba(0, 255, 0, 0.2);
}

.pokemon-3d-viewer.error {
  border-color: #ff6b6b;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.2);
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #333;
}

.pokemon-name {
  color: #00ff00;
  font-weight: 600;
  font-size: 16px;
  text-transform: capitalize;
}

.status {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.status.loading {
  color: #ffeb3b;
  background: rgba(255, 235, 59, 0.1);
  animation: pulse 2s infinite;
}

.status.loaded {
  color: #00ff00;
  background: rgba(0, 255, 0, 0.1);
}

.status.error {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
}

.model-container {
  margin: 15px 0;
  border-radius: 8px;
  overflow: hidden;
  background: #0a0a0a;
  border: 1px solid #333;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #00ff00;
}

.loading-text {
  margin-bottom: 10px;
  font-size: 14px;
}

.loading-spinner {
  font-size: 24px;
  animation: spin 1s linear infinite;
}

.viewer-controls {
  display: flex;
  justify-content: space-around;
  margin: 15px 0;
  padding: 10px;
  background: rgba(0, 255, 0, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 0, 0.2);
}

.control-hint {
  color: #61dafb;
  font-size: 12px;
  text-align: center;
}

.pokemon-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #333;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.label {
  color: #888;
  font-size: 13px;
  font-weight: 500;
}

.value {
  color: #00ff00;
  font-size: 13px;
  font-weight: 600;
  text-transform: capitalize;
}

.error-content {
  color: #ff6b6b;
  padding: 20px;
  text-align: center;
}

.error-content p {
  margin: 10px 0;
  line-height: 1.5;
}

.error-content ul {
  text-align: left;
  margin: 15px 0;
  padding-left: 20px;
}

.error-content li {
  margin: 5px 0;
  color: #ffeb3b;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .pokemon-3d-viewer {
    margin: 10px 0;
    padding: 10px;
  }
  
  .pokemon-info {
    grid-template-columns: 1fr;
  }
  
  .viewer-controls {
    flex-direction: column;
    gap: 5px;
  }
  
  .model-container model-viewer {
    height: 250px !important;
  }
}

/* Custom scrollbar for the viewer */
.pokemon-3d-viewer::-webkit-scrollbar {
  width: 6px;
}

.pokemon-3d-viewer::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.pokemon-3d-viewer::-webkit-scrollbar-thumb {
  background: #00ff00;
  border-radius: 3px;
}

.pokemon-3d-viewer::-webkit-scrollbar-thumb:hover {
  background: #00cc00;
}
