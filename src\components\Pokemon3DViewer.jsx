import React, { useEffect, useRef, useState } from 'react'
import './Pokemon3DViewer.css'

const Pokemon3DViewer = ({ pokemonName, modelUrl, pokemonData }) => {
  const [modelLoaded, setModelLoaded] = useState(false)
  const [modelError, setModelError] = useState(false)
  const viewerRef = useRef(null)

  useEffect(() => {
    const checkModel = async () => {
      try {
        const response = await fetch(modelUrl, { method: 'HEAD' })
        if (!response.ok) {
          setModelError(true)
        }
      } catch (error) {
        setModelError(true)
      }
    }
    
    checkModel()
  }, [modelUrl])

  const handleModelLoad = () => {
    setModelLoaded(true)
  }

  const handleModelError = () => {
    setModelError(true)
  }

  if (modelError) {
    return (
      <div className="pokemon-3d-viewer error">
        <div className="viewer-header">
          <span className="pokemon-name">🎮 {pokemonName}</span>
          <span className="status error">❌ 3D Model Not Available</span>
        </div>
        <div className="error-content">
          <p>The 3D model for {pokemonName} is not available in the Pokemon 3D API.</p>
          <p>You can try:</p>
          <ul>
            <li>Checking the spelling of the Pokemon name</li>
            <li>Trying a different Pokemon</li>
            <li>Visiting the API repository for available models</li>
          </ul>
        </div>
      </div>
    )
  }

  return (
    <div className="pokemon-3d-viewer">
      <div className="viewer-header">
        <span className="pokemon-name">🎮 3D Model - {pokemonName}</span>
        <span className={`status ${modelLoaded ? 'loaded' : 'loading'}`}>
          {modelLoaded ? '✅ Loaded' : '🔄 Loading...'}
        </span>
      </div>
      
      <div className="model-container">
        <model-viewer
          ref={viewerRef}
          src={modelUrl}
          alt={`3D model of ${pokemonName}`}
          auto-rotate
          auto-rotate-delay="0"
          rotation-per-second="30deg"
          loading="eager"
          reveal="auto"
          camera-orbit="0deg 75deg 1.5m"
          field-of-view="45deg"
          min-camera-orbit="auto auto 1m"
          max-camera-orbit="auto auto 3m"
          onLoad={handleModelLoad}
          onError={handleModelError}
          style={{
            width: '100%',
            height: '300px',
            backgroundColor: '#0a0a0a',
            borderRadius: '8px'
          }}
        >
          <div slot="progress-bar" className="loading-indicator">
            <div className="loading-text">Loading 3D model...</div>
            <div className="loading-spinner">⚡</div>
          </div>
        </model-viewer>
      </div>
      
      <div className="viewer-controls">
        <span className="control-hint">🔄 Auto-rotating 3D model</span>
        <span className="control-hint">✨ Optimized for viewing</span>
      </div>
      
      <div className="pokemon-info">
        <div className="info-row">
          <span className="label">ID:</span>
          <span className="value">#{pokemonData.id.toString().padStart(3, '0')}</span>
        </div>
        <div className="info-row">
          <span className="label">Type:</span>
          <span className="value">{pokemonData.types.map(type => type.type.name).join(', ')}</span>
        </div>
        <div className="info-row">
          <span className="label">Height:</span>
          <span className="value">{pokemonData.height / 10} m</span>
        </div>
        <div className="info-row">
          <span className="label">Weight:</span>
          <span className="value">{pokemonData.weight / 10} kg</span>
        </div>
      </div>
    </div>
  )
}

export default Pokemon3DViewer
