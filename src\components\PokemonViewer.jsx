import React, { useState, useEffect, useRef } from 'react'
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import './PokemonViewer.css'

const PokemonViewer = ({ pokemonName, pokemonData }) => {
  const [modelLoaded, setModelLoaded] = useState(false)
  const [modelError, setModelError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const mountRef = useRef(null)
  const sceneRef = useRef(null)
  const rendererRef = useRef(null)
  const animationRef = useRef(null)

  // Get 3D model URL from Pokemon 3D API
  const getModelUrl = (id) => {
    return `https://raw.githubusercontent.com/Sudhanshu-Ambastha/Pokemon-3D/main/models/glb/regular/${id}.glb`
  }

  // Get fallback artwork URL
  const getArtworkUrl = (id) => {
    return `https://raw.githubusercontent.com/PokeAPI/sprites/master/sprites/pokemon/other/official-artwork/${id}.png`
  }

  useEffect(() => {
    if (!pokemonData?.id || !mountRef.current) return

    const initThreeJS = () => {
      // Scene setup
      const scene = new THREE.Scene()
      scene.background = new THREE.Color(0x0a0a0a)
      sceneRef.current = scene

      // Camera setup
      const camera = new THREE.PerspectiveCamera(
        45,
        mountRef.current.clientWidth / mountRef.current.clientHeight,
        0.1,
        1000
      )
      camera.position.set(0, 1, 3)

      // Renderer setup
      const renderer = new THREE.WebGLRenderer({ antialias: true })
      renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight)
      renderer.shadowMap.enabled = true
      renderer.shadowMap.type = THREE.PCFSoftShadowMap
      rendererRef.current = renderer
      mountRef.current.appendChild(renderer.domElement)

      // Lighting
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      scene.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(5, 5, 5)
      directionalLight.castShadow = true
      scene.add(directionalLight)

      const pointLight = new THREE.PointLight(0x00ff00, 0.3)
      pointLight.position.set(-5, 3, 2)
      scene.add(pointLight)

      // Load 3D model
      const loader = new GLTFLoader()
      const modelUrl = getModelUrl(pokemonData.id)

      loader.load(
        modelUrl,
        (gltf) => {
          const model = gltf.scene

          // Center and scale the model
          const box = new THREE.Box3().setFromObject(model)
          const center = box.getCenter(new THREE.Vector3())
          const size = box.getSize(new THREE.Vector3())

          model.position.sub(center)
          const maxDim = Math.max(size.x, size.y, size.z)
          const scale = 2 / maxDim
          model.scale.setScalar(scale)

          // Enable shadows
          model.traverse((child) => {
            if (child.isMesh) {
              child.castShadow = true
              child.receiveShadow = true
            }
          })

          scene.add(model)
          setModelLoaded(true)
          setIsLoading(false)

          // Animation loop
          const animate = () => {
            animationRef.current = requestAnimationFrame(animate)
            model.rotation.y += 0.01
            renderer.render(scene, camera)
          }
          animate()
        },
        (progress) => {
          // Loading progress
          console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%')
        },
        (error) => {
          console.error('Error loading 3D model:', error)
          setModelError(true)
          setIsLoading(false)
        }
      )

      // Handle resize
      const handleResize = () => {
        if (mountRef.current) {
          camera.aspect = mountRef.current.clientWidth / mountRef.current.clientHeight
          camera.updateProjectionMatrix()
          renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight)
        }
      }
      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
      }
    }

    const cleanup = initThreeJS()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      if (rendererRef.current && mountRef.current) {
        mountRef.current.removeChild(rendererRef.current.domElement)
        rendererRef.current.dispose()
      }
      if (cleanup) cleanup()
    }
  }, [pokemonData])

  const handleImageLoad = () => {
    setModelLoaded(true)
    setIsLoading(false)
  }

  const handleImageError = () => {
    setModelError(true)
    setIsLoading(false)
  }

  const getTypeColor = (type) => {
    const colors = {
      normal: '#A8A878',
      fire: '#F08030',
      water: '#6890F0',
      electric: '#F8D030',
      grass: '#78C850',
      ice: '#98D8D8',
      fighting: '#C03028',
      poison: '#A040A0',
      ground: '#E0C068',
      flying: '#A890F0',
      psychic: '#F85888',
      bug: '#A8B820',
      rock: '#B8A038',
      ghost: '#705898',
      dragon: '#7038F8',
      dark: '#705848',
      steel: '#B8B8D0',
      fairy: '#EE99AC'
    }
    return colors[type] || '#68A090'
  }

  return (
    <div className="pokemon-viewer-container">
      <div className="pokemon-header">
        <h3>🎮 Pokemon Viewer - {pokemonName}</h3>
        <span className="pokemon-id">#{pokemonData?.id?.toString().padStart(3, '0') || 'Loading...'}</span>
      </div>
      
      <div className="pokemon-display">
        <div className="pokemon-model-container">
          {isLoading && (
            <div className="loading-indicator">
              <div className="loading-spinner">⚡</div>
              <div className="loading-text">Loading 3D model...</div>
            </div>
          )}

          {!modelError ? (
            <div
              ref={mountRef}
              className="threejs-container"
              style={{
                width: '100%',
                height: '200px',
                display: isLoading ? 'none' : 'block'
              }}
            />
          ) : (
            <div className="model-fallback">
              <img
                src={getArtworkUrl(pokemonData?.id)}
                alt={pokemonName}
                className="pokemon-artwork-fallback"
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
              <div className="fallback-text">3D model unavailable - showing artwork</div>
            </div>
          )}
        </div>

        <div className="pokemon-info">
          <div className="pokemon-types">
            {pokemonData?.types?.map((type, index) => (
              <span
                key={index}
                className="type-badge"
                style={{ backgroundColor: getTypeColor(type.type.name) }}
              >
                {type.type.name}
              </span>
            ))}
          </div>
        </div>
      </div>

      <div className="pokemon-stats">
        <div className="stat-row">
          <span className="stat-label">📊 Type:</span>
          <span className="stat-value">
            {pokemonData?.types?.map(type => type.type.name).join(', ') || 'Loading...'}
          </span>
        </div>
        <div className="stat-row">
          <span className="stat-label">⚖️ Weight:</span>
          <span className="stat-value">
            {pokemonData?.weight ? `${(pokemonData.weight / 10).toFixed(1)} kg` : 'Loading...'}
          </span>
        </div>
        <div className="stat-row">
          <span className="stat-label">📏 Height:</span>
          <span className="stat-value">
            {pokemonData?.height ? `${(pokemonData.height / 10).toFixed(1)} m` : 'Loading...'}
          </span>
        </div>
        <div className="stat-row">
          <span className="stat-label">⚡ Base Experience:</span>
          <span className="stat-value">
            {pokemonData?.base_experience || 'Unknown'}
          </span>
        </div>
      </div>

      <div className="pokemon-abilities">
        <h4>🎯 Abilities:</h4>
        <div className="abilities-list">
          {pokemonData?.abilities?.map((ability, index) => (
            <span key={index} className="ability-badge">
              {ability.ability.name.replace('-', ' ')}
              {ability.is_hidden && <span className="hidden-tag">(Hidden)</span>}
            </span>
          ))}
        </div>
      </div>

      <div className="pokemon-base-stats">
        <h4>📈 Base Stats:</h4>
        <div className="stats-grid">
          {pokemonData?.stats?.map((stat, index) => (
            <div key={index} className="stat-item">
              <span className="stat-name">{stat.stat.name.replace('-', ' ')}</span>
              <div className="stat-bar">
                <div 
                  className="stat-fill" 
                  style={{ width: `${Math.min(stat.base_stat / 2, 100)}%` }}
                ></div>
                <span className="stat-number">{stat.base_stat}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default PokemonViewer
