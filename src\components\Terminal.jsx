import React, { useState, useEffect, useRef } from 'react'
import './Terminal.css'

// Simple commands for testing
const commands = {
  execute(command) {
    const [cmd, ...args] = command.split(' ')
    const argument = args.join(' ')

    switch (cmd) {
      case 'help':
        return {
          type: 'info',
          content: `Available commands:

📋 Portfolio Commands:
  help       - Show this help message
  about      - Learn about Mark Gatere
  skills     - View technical skills
  projects   - See portfolio projects
  experience - View work experience
  education  - Educational background
  contact    - Get contact information
  resume     - Download resume
  social     - Social media links

🛠️  System Commands:
  clear      - Clear the terminal
  echo       - Echo back text
  whoami     - Display current user
  date       - Show current date
  pwd        - Print working directory
  ls         - List directory contents
  cat        - Display file contents
  neofetch   - System information

🎮 Fun Commands:
  matrix     - Enter the matrix...
  joke       - Programming joke
  quote      - Inspirational quote

💡 Tips:
  • Use ↑/↓ arrows for command history
  • Use Tab for autocomplete
  • Type any command to get started!`
        }
      case 'about':
        return {
          type: 'typing',
          content: [
            '👋 Hello! I\'m <PERSON>',
            '',
            'I\'m a passionate Software Developer and Creative Technologist',
            'with expertise in modern web technologies and 3D graphics.',
            '',
            '🚀 What I do:',
            '   • Full-stack web development',
            '   • Interactive 3D experiences',
            '   • Modern React applications',
            '   • Creative coding and digital art',
            '',
            '💡 I believe in the power of technology to solve real-world',
            '   problems and create meaningful digital experiences.',
            '',
            'Type "skills" to see my technical expertise!'
          ]
        }
      case 'skills':
        return {
          type: 'success',
          content: `🛠️  Technical Skills:

Frontend: JavaScript, TypeScript, React, Next.js, Vue.js
Backend:  Node.js, Python, PHP, Express.js
Database: MongoDB, PostgreSQL, MySQL
Tools:    Git, Docker, AWS, Vercel
3D/Graphics: Three.js, WebGL, Blender`
        }
      case 'projects':
        return {
          type: 'success',
          content: `📁 Featured Projects:

1. 🌐 Terminal Portfolio (Current)
   Tech: React, CSS3, JavaScript

2. 🎮 3D Web Game Engine
   Tech: Three.js, WebGL

3. 📱 E-commerce Platform
   Tech: Next.js, MongoDB

4. 🤖 AI Chat Application
   Tech: React, Node.js, OpenAI`
        }
      case 'contact':
        return {
          type: 'info',
          content: `📧 Contact Information:

Email:    <EMAIL>
LinkedIn: https://linkedin.com/in/markgatere
GitHub:   https://github.com/markgatere
Website:  https://markgatere.dev

💬 Available for freelance projects and collaborations!`
        }
      case 'experience':
        return {
          type: 'info',
          content: `💼 Work Experience:

Senior Frontend Developer | TechCorp Inc.
📅 2022 - Present
├── Led development of React-based web applications
├── Implemented 3D visualizations using Three.js
├── Mentored junior developers
└── Improved application performance by 40%

Full Stack Developer | StartupXYZ
📅 2020 - 2022
├── Built scalable web applications with MERN stack
├── Designed and implemented RESTful APIs
├── Collaborated with design team on UX/UI
└── Deployed applications on AWS

Junior Developer | WebSolutions Ltd.
📅 2019 - 2020
├── Developed responsive websites
├── Worked with WordPress and PHP
└── Learned modern development practices`
        }
      case 'education':
        return {
          type: 'info',
          content: `🎓 Education:

Bachelor of Science in Computer Science
📅 University of Technology | 2015 - 2019
├── Graduated Magna Cum Laude
├── Focus: Software Engineering & Web Development
└── Final Project: 3D Web-based Game Engine

📚 Certifications:
├── AWS Certified Developer Associate
├── React Developer Certification
└── MongoDB Certified Developer`
        }
      case 'resume':
        return {
          type: 'success',
          content: `📄 Resume Download:

┌─────────────────────────────────────┐
│  Mark_Gatere_Resume_2024.pdf        │
│  ▓▓▓▓▓▓▓▓▓▓ 100% Downloaded         │
└─────────────────────────────────────┘

✅ Resume downloaded successfully!

Note: This is a demo. In a real implementation,
this would trigger an actual file download.`
        }
      case 'social':
        return {
          type: 'info',
          content: `🌐 Social Media & Links:

Professional:
├── 💼 LinkedIn:  https://linkedin.com/in/markgatere
├── 🐙 GitHub:    https://github.com/markgatere
├── 📧 Email:     <EMAIL>
└── 🌍 Website:   https://markgatere.dev

Creative:
├── 🎨 Dribbble:  https://dribbble.com/markgatere
├── 🐦 Twitter:   https://twitter.com/markgatere
└── 📷 Instagram: https://instagram.com/markgatere`
        }
      case 'whoami':
        return {
          type: 'output',
          content: 'mark'
        }
      case 'date':
        return {
          type: 'output',
          content: new Date().toString()
        }
      case 'pwd':
        return {
          type: 'output',
          content: '/home/<USER>/portfolio'
        }
      case 'ls':
        return {
          type: 'output',
          content: `projects/     skills/       experience/   education/
about.txt     contact.txt   resume.pdf    social.txt
README.md     .gitignore    package.json  node_modules/`
        }
      case 'cat':
        const files = {
          'about.txt': 'Mark Gatere - Software Developer & Creative Technologist',
          'contact.txt': 'Email: <EMAIL>\nPhone: +****************',
          'readme.md': '# Mark Gatere\'s Portfolio Terminal\n\nWelcome to my interactive terminal portfolio!\n\nType "help" to get started.'
        }

        if (!argument) {
          return {
            type: 'error',
            content: 'cat: missing file operand\nTry: cat <filename>'
          }
        }

        const file = files[argument.toLowerCase()]
        if (file) {
          return {
            type: 'output',
            content: file
          }
        } else {
          return {
            type: 'error',
            content: `cat: ${argument}: No such file or directory`
          }
        }
      case 'neofetch':
        return {
          type: 'info',
          content: `                    .-/+oossssoo+/-.               mark@portfolio
                .:+ssssssssssssssss+:.           ----------------
              -+ssssssssssssssssssssss+-         OS: Portfolio Linux
            -+sssssssssssssssssssssssssss+-      Host: Creative Workstation
          -+sssssssssssssssssssssssssssssss+-    Kernel: 5.15.0-creativity
        .+sssssssssssssssssssssssssssssssssss+.  Uptime: 5 years, 2 months
       +sssssssssssssssssssssssssssssssssssssss+ Shell: portfolio-terminal
      +ssssssssssssssssssssssssssssssssssssssss+ Resolution: ∞x∞
     .ssssssssssssssssssssssssssssssssssssssss.  DE: React Desktop Environment
     .ssssssssssssssssssssssssssssssssssssssss.  WM: JavaScript Window Manager
      +ssssssssssssssssssssssssssssssssssssssss+ Theme: Dark Terminal
       +sssssssssssssssssssssssssssssssssssssss+ Terminal: mark-terminal
        .+sssssssssssssssssssssssssssssssssss+.  CPU: Creative Brain (8) @ 3.5GHz
          -+sssssssssssssssssssssssssssssss+-    Memory: Unlimited`
        }
      case 'matrix':
        return {
          type: 'success',
          content: `Wake up, Neo...

The Matrix has you...

Follow the white rabbit.

01001000 01100101 01101100 01101100 01101111
01001110 01100101 01101111

Welcome to the real world.`
        }
      case 'joke':
        const jokes = [
          "Why do programmers prefer dark mode? Because light attracts bugs!",
          "How many programmers does it take to change a light bulb? None, that's a hardware problem.",
          "Why do Java developers wear glasses? Because they can't C#!",
          "A SQL query goes into a bar, walks up to two tables and asks: 'Can I join you?'",
          "Why did the programmer quit his job? He didn't get arrays!"
        ]
        const randomJoke = jokes[Math.floor(Math.random() * jokes.length)]
        return {
          type: 'success',
          content: `😄 ${randomJoke}`
        }
      case 'quote':
        const quotes = [
          '"Code is like humor. When you have to explain it, it\'s bad." - Cory House',
          '"First, solve the problem. Then, write the code." - John Johnson',
          '"Experience is the name everyone gives to their mistakes." - Oscar Wilde',
          '"Java is to JavaScript what car is to Carpet." - Chris Heilmann'
        ]
        const randomQuote = quotes[Math.floor(Math.random() * quotes.length)]
        return {
          type: 'info',
          content: `💭 ${randomQuote}`
        }
      case 'sudo':
        if (argument === 'rm -rf /') {
          return {
            type: 'error',
            content: 'Nice try! 😄 This is a portfolio, not a real terminal!'
          }
        }
        return {
          type: 'error',
          content: 'sudo: command not found. This is a portfolio terminal!'
        }
      case 'exit':
      case 'quit':
        return {
          type: 'info',
          content: 'Thanks for visiting! 👋 Refresh the page to restart the terminal.'
        }
      case 'vim':
      case 'nano':
        return {
          type: 'error',
          content: 'Text editors not available in portfolio mode. Try "cat" to read files!'
        }
      case 'rm':
        return {
          type: 'error',
          content: 'rm: Permission denied. Portfolio files are protected! 🛡️'
        }
      case 'hack':
      case 'hacker':
        return {
          type: 'success',
          content: `🕶️ HACKER MODE ACTIVATED 🕶️

Accessing mainframe...
[████████████████████████████████] 100%

Just kidding! This is a portfolio terminal.
Try "matrix" for some real fun! 😎`
        }
      case 'coffee':
        return {
          type: 'success',
          content: `☕ Brewing coffee...

[████████████████████████████████] 100%

Your virtual coffee is ready! ☕
Perfect fuel for coding sessions.

Fun fact: I run on coffee and clean code! ☕💻`
        }
      case 'echo':
        return {
          type: 'output',
          content: argument || ''
        }
      default:
        // Easter egg for common typos
        if (cmd === 'hlep' || cmd === 'halp') {
          return {
            type: 'info',
            content: 'Did you mean "help"? 😊'
          }
        }
        if (cmd === 'claer' || cmd === 'clar') {
          return {
            type: 'info',
            content: 'Did you mean "clear"? 😊'
          }
        }
        return {
          type: 'error',
          content: `Command not found: ${cmd}. Type 'help' for available commands.`
        }
    }
  }
}

const Terminal = () => {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState([])
  const [commandHistory, setCommandHistory] = useState([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [isTyping, setIsTyping] = useState(false)
  const inputRef = useRef(null)
  const terminalRef = useRef(null)

  // Welcome message
  useEffect(() => {
    const welcomeMessage = [
      { type: 'success', content: '╔══════════════════════════════════════════════════╗' },
      { type: 'success', content: '║        Welcome to Pratham Sharda\'s Portfolio      ║' },
      { type: 'success', content: '║              Terminal Interface v1.0             ║' },
      { type: 'success', content: '╚══════════════════════════════════════════════════╝' },
      { type: 'info', content: '' },
      { type: 'info', content: '🚀 Interactive Portfolio Terminal' },
      { type: 'info', content: '👈 Try dragging the 3D ID card on the left!' },
      { type: 'info', content: '💻 Type "help" to see available commands' },
      { type: 'info', content: '🎯 Try "about" to learn more about Pratham Sharda' },
      { type: 'info', content: '✨ Use Tab for autocomplete, ↑/↓ for history' },
      { type: 'info', content: '' },
      { type: 'output', content: 'System initialized. Ready for commands...' },
      { type: 'info', content: '─'.repeat(50) }
    ]
    setOutput(welcomeMessage)
  }, [])

  // Auto-focus input and scroll to bottom
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [output])

  // Handle command execution
  const executeCommand = async (command) => {
    const trimmedCommand = command.trim().toLowerCase()
    
    // Add command to history
    if (trimmedCommand && !commandHistory.includes(trimmedCommand)) {
      setCommandHistory(prev => [...prev, trimmedCommand])
    }
    
    // Add command to output
    setOutput(prev => [...prev, { type: 'command', content: command }])
    
    // Handle special commands
    if (trimmedCommand === 'clear') {
      setOutput([])
      return
    }
    
    // Execute command
    const result = await commands.execute(trimmedCommand)
    
    if (result.type === 'typing') {
      setIsTyping(true)
      await typeMessage(result.content)
      setIsTyping(false)
    } else {
      setOutput(prev => [...prev, result])
    }
  }

  // Typing effect for special commands
  const typeMessage = (message) => {
    return new Promise((resolve) => {
      let index = 0
      const lines = Array.isArray(message) ? message : [message]
      
      const typeNextLine = () => {
        if (index < lines.length) {
          const line = lines[index]
          setOutput(prev => [...prev, { type: 'typing', content: '', id: Date.now() + index }])
          
          let charIndex = 0
          const typeChar = () => {
            if (charIndex < line.length) {
              setOutput(prev => {
                const newOutput = [...prev]
                const lastItem = newOutput[newOutput.length - 1]
                if (lastItem.type === 'typing') {
                  lastItem.content = line.substring(0, charIndex + 1)
                }
                return newOutput
              })
              charIndex++
              setTimeout(typeChar, 20)
            } else {
              // Convert typing to regular output
              setOutput(prev => {
                const newOutput = [...prev]
                const lastItem = newOutput[newOutput.length - 1]
                if (lastItem.type === 'typing') {
                  lastItem.type = 'success'
                }
                return newOutput
              })
              index++
              setTimeout(typeNextLine, 100)
            }
          }
          typeChar()
        } else {
          resolve()
        }
      }
      typeNextLine()
    })
  }

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault()
    if (input.trim() && !isTyping) {
      executeCommand(input)
      setInput('')
      setHistoryIndex(-1)
    }
  }

  // Handle key navigation
  const handleKeyDown = (e) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault()
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1)
        setHistoryIndex(newIndex)
        setInput(commandHistory[newIndex])
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1)
          setInput('')
        } else {
          setHistoryIndex(newIndex)
          setInput(commandHistory[newIndex])
        }
      }
    } else if (e.key === 'Tab') {
      e.preventDefault()
      // Enhanced autocomplete
      const availableCommands = [
        'help', 'about', 'skills', 'projects', 'experience', 'education',
        'contact', 'resume', 'social', 'clear', 'echo', 'whoami', 'date',
        'pwd', 'ls', 'cat', 'neofetch', 'sudo', 'exit', 'quit', 'hack',
        'coffee', 'pokedex'
      ]
      const matches = availableCommands.filter(cmd => cmd.startsWith(input.toLowerCase()))
      if (matches.length === 1) {
        setInput(matches[0])
      } else if (matches.length > 1) {
        // Show available matches
        setOutput(prev => [...prev,
          { type: 'command', content: input },
          { type: 'info', content: `Available completions: ${matches.join(', ')}` }
        ])
      }
    }
  }

  return (
    <div className="terminal-container" onClick={() => inputRef.current?.focus()}>
      <div className="terminal-header">
        <div className="terminal-buttons">
          <span className="terminal-button close"></span>
          <span className="terminal-button minimize"></span>
          <span className="terminal-button maximize"></span>
        </div>
        <div className="terminal-title">pstorque:~$</div>
      </div>
      
      <div className="terminal-body" ref={terminalRef}>
        <div className="terminal-output">
          {output.map((line, index) => (
            <div key={index} className={`terminal-line ${line.type}`}>
              {line.type === 'command' && <span className="prompt">$</span>}
              {line.isComponent ? (
                <div className="content">{line.content}</div>
              ) : line.isHtml ? (
                <div className="content" dangerouslySetInnerHTML={{ __html: line.content }} />
              ) : (
                <span className="content">{line.content}</span>
              )}
              {line.type === 'typing' && <span className="cursor">█</span>}
            </div>
          ))}
        </div>
        
        <form onSubmit={handleSubmit} className="terminal-input-form">
          <div className="terminal-input-line">
            <span className="prompt">$</span>
            <div className="input-container">
              <span className="input-text">{input}</span>
              <span className="cursor blinking">█</span>
              <input
                ref={inputRef}
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                className="terminal-input"
                disabled={isTyping}
                autoComplete="off"
                spellCheck="false"
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default Terminal
