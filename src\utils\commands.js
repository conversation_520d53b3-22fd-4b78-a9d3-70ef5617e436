// Terminal commands system
import React from 'react'
import Pokemon3DViewer from '../components/Pokemon3DViewer.jsx'

export const commands = {
  list: {
    help: 'Show available commands',
    about: 'Learn more about <PERSON><PERSON><PERSON>',
    skills: 'Display technical skills and expertise',
    projects: 'View portfolio projects',
    experience: 'Show work experience',
    education: 'Display educational background',
    contact: 'Get contact information',
    resume: 'Download resume',
    social: 'Show social media links',
    echo: 'Echo back the input text',
    clear: 'Clear the terminal screen',
    whoami: 'Display current user info',
    date: 'Show current date and time',
    pwd: 'Print working directory',
    ls: 'List directory contents',
    cat: 'Display file contents',
    neofetch: 'Display system information',
    pokedex: 'Show 3D Pokemon - Usage: pokedex show -<pokemon_name>'
  },

  async execute(command) {
    const [cmd, ...args] = command.split(' ')
    const argument = args.join(' ')
    const cmdLower = cmd.toLowerCase()

    console.log('Executing command:', cmdLower, 'with argument:', argument)

    switch (cmdLower) {
      case 'help':
        return this.help()
      case 'about':
        return this.about()
      case 'skills':
        return this.skills()
      case 'projects':
        return this.projects()
      case 'experience':
        return this.experience()
      case 'education':
        return this.education()
      case 'contact':
        return this.contact()
      case 'resume':
        return this.resume()
      case 'social':
        return this.social()
      case 'echo':
        return this.echo(argument)
      case 'whoami':
        return this.whoami()
      case 'date':
        return this.date()
      case 'pwd':
        return this.pwd()
      case 'ls':
        return this.ls()
      case 'cat':
        return this.cat(argument)
      case 'neofetch':
        return this.neofetch()
      case 'pokedex':
        return this.pokedex(argument)
      default:
        return {
          type: 'error',
          content: `Command not found: ${cmd}. Type 'help' for available commands.`
        }
    }
  },

  help() {
    const commandList = Object.entries(this.list)
      .map(([cmd, desc]) => `  ${cmd.padEnd(12)} - ${desc}`)
      .join('\n')

    return {
      type: 'info',
      content: `Available commands:\n\n${commandList}\n\n🎮 Pokedex Usage:\n  pokedex show -<pokemon_name>\n  Examples:\n    pokedex show -pikachu\n    pokedex show -charizard\n    pokedex show -mewtwo\n\n💡 Tips:\n  • Use Tab for autocomplete\n  • Use ↑/↓ arrows for command history\n  • Type 'clear' to clear the terminal\n  • Commands are case-sensitive`
    }
  },

  about() {
    return {
      type: 'typing',
      content: [
        '👋 Hello! I\'m Pratham Sharda',
        '',
        'I\'m a passionate Software Developer and Creative Technologist',
        'with a love for building innovative web experiences.',
        '',
        '🚀 What I do:',
        '   • Full-stack web development',
        '   • Interactive 3D experiences with Three.js',
        '   • Modern React applications',
        '   • Creative coding and digital art',
        '',
        '💡 I believe in the power of technology to solve real-world',
        '   problems and create meaningful digital experiences.',
        '',
        'Type "skills" to see my technical expertise!'
      ]
    }
  },

  skills() {
    return {
      type: 'success',
      content: `
🛠️  Technical Skills:

Frontend:
  ├── JavaScript (ES6+)
  ├── TypeScript
  ├── React.js
  ├── Next.js
  ├── Vue.js
  ├── HTML5 & CSS3
  ├── Tailwind CSS
  └── Three.js / WebGL

Backend:
  ├── Node.js
  ├── Express.js
  ├── Python
  ├── Django/Flask
  ├── PHP
  └── RESTful APIs

Database:
  ├── MongoDB
  ├── PostgreSQL
  ├── MySQL
  └── Firebase

Tools & Others:
  ├── Git & GitHub
  ├── Docker
  ├── AWS
  ├── Vercel
  ├── Figma
  └── Linux/Unix

Currently learning: Rust, WebAssembly, AI/ML
`
    }
  },

  projects() {
    return {
      type: 'success',
      content: `
📁 Featured Projects:

1. 🌐 Interactive Portfolio Terminal
   ├── Tech: React, CSS3, JavaScript
   ├── Features: Command-line interface, typing effects
   └── Status: You're using it right now! 

2. 🎮 3D Web Game Engine
   ├── Tech: Three.js, WebGL, JavaScript
   ├── Features: Physics simulation, 3D graphics
   └── Status: In development

3. 📱 E-commerce Platform
   ├── Tech: Next.js, MongoDB, Stripe
   ├── Features: Full-stack, payment integration
   └── Status: Completed

4. 🤖 AI Chat Application
   ├── Tech: React, Node.js, OpenAI API
   ├── Features: Real-time chat, AI responses
   └── Status: Completed

5. 📊 Data Visualization Dashboard
   ├── Tech: D3.js, React, Python
   ├── Features: Interactive charts, real-time data
   └── Status: Completed

Type 'contact' to discuss potential collaborations!
`
    }
  },

  experience() {
    return {
      type: 'info',
      content: `
💼 Work Experience:

Senior Frontend Developer | TechCorp Inc.
📅 2022 - Present
├── Led development of React-based web applications
├── Implemented 3D visualizations using Three.js
├── Mentored junior developers
└── Improved application performance by 40%

Full Stack Developer | StartupXYZ
📅 2020 - 2022
├── Built scalable web applications with MERN stack
├── Designed and implemented RESTful APIs
├── Collaborated with design team on UX/UI
└── Deployed applications on AWS

Junior Developer | WebSolutions Ltd.
📅 2019 - 2020
├── Developed responsive websites with HTML/CSS/JS
├── Worked with WordPress and PHP
├── Learned modern development practices
└── Contributed to team projects

Freelance Developer
📅 2018 - 2019
├── Built custom websites for small businesses
├── Learned client communication skills
└── Developed project management abilities
`
    }
  },

  education() {
    return {
      type: 'info',
      content: `
🎓 Education:

Bachelor of Science in Computer Science
📅 University of Technology | 2015 - 2019
├── Graduated Magna Cum Laude
├── Focus: Software Engineering & Web Development
├── Relevant Coursework:
│   ├── Data Structures & Algorithms
│   ├── Database Systems
│   ├── Software Engineering
│   ├── Computer Graphics
│   └── Human-Computer Interaction
└── Final Project: 3D Web-based Game Engine

📚 Certifications:
├── AWS Certified Developer Associate
├── React Developer Certification
├── MongoDB Certified Developer
└── Google Analytics Certified

🏆 Achievements:
├── Dean's List (4 semesters)
├── Hackathon Winner (2018)
└── Computer Science Student of the Year (2019)
`
    }
  },

  contact() {
    return {
      type: 'success',
      content: `
📧 Contact Information:

Email:    <EMAIL>
Phone:    +****************
Location: San Francisco, CA

🌐 Online Presence:
├── Portfolio:  https://prathamsharda.dev
├── LinkedIn:   https://linkedin.com/in/prathamsharda
├── GitHub:     https://github.com/prathamsharda
├── Twitter:    https://twitter.com/prathamsharda
└── Dribbble:   https://dribbble.com/prathamsharda

💬 Preferred Contact Methods:
1. Email (for professional inquiries)
2. LinkedIn (for networking)
3. GitHub (for technical discussions)

📅 Available for:
├── Full-time opportunities
├── Freelance projects
├── Technical consulting
└── Speaking engagements

Response time: Usually within 24 hours
`
    }
  },

  resume() {
    return {
      type: 'success',
      content: `
📄 Resume Download:

┌─────────────────────────────────────┐
│  Pratham_Sharda_Resume_2024.pdf     │
│  ▓▓▓▓▓▓▓▓▓▓ 100% Downloaded         │
└─────────────────────────────────────┘

✅ Resume downloaded successfully!

📋 Quick Summary:
├── 5+ years of software development experience
├── Expert in React, Node.js, and modern web technologies
├── Strong background in 3D graphics and creative coding
├── Proven track record of delivering high-quality projects
└── Passionate about creating innovative user experiences

Note: This is a demo. In a real implementation, 
this would trigger an actual file download.
`
    }
  },

  social() {
    return {
      type: 'info',
      content: `
🌐 Social Media & Professional Links:

Professional:
├── 💼 LinkedIn:  https://linkedin.com/in/prathamsharda
├── 🐙 GitHub:    https://github.com/prathamsharda
├── 📧 Email:     <EMAIL>
└── 🌍 Website:   https://prathamsharda.dev

Creative:
├── 🎨 Dribbble:  https://dribbble.com/prathamsharda
├── 📷 Instagram: https://instagram.com/prathamsharda
├── 🐦 Twitter:   https://twitter.com/prathamsharda
└── 📺 YouTube:   https://youtube.com/prathamsharda

Development:
├── 📦 NPM:       https://npmjs.com/~prathamsharda
├── 🏗️  CodePen:   https://codepen.io/prathamsharda
├── 📚 Dev.to:    https://dev.to/prathamsharda
└── 🔗 Linktree:  https://linktr.ee/prathamsharda

Feel free to connect on any platform!
`
    }
  },

  echo(text) {
    return {
      type: 'output',
      content: text || ''
    }
  },

  whoami() {
    return {
      type: 'output',
      content: 'pratham'
    }
  },

  date() {
    return {
      type: 'output',
      content: new Date().toString()
    }
  },

  pwd() {
    return {
      type: 'output',
      content: '/home/<USER>/portfolio'
    }
  },

  ls() {
    return {
      type: 'output',
      content: `
projects/     skills/       experience/   education/
about.txt     contact.txt   resume.pdf    social.txt
README.md     .gitignore    package.json  node_modules/
`
    }
  },

  cat(filename) {
    const files = {
      'about.txt': 'Pratham Sharda - Software Developer & Creative Technologist',
      'contact.txt': 'Email: <EMAIL>\nPhone: +****************',
      'readme.md': '# Pratham Sharda\'s Portfolio Terminal\n\nWelcome to my interactive terminal portfolio!\n\nType "help" to get started.',
      'package.json': '{\n  "name": "pratham-sharda-portfolio",\n  "version": "1.0.0",\n  "description": "Interactive terminal portfolio"\n}'
    }

    if (!filename) {
      return {
        type: 'error',
        content: 'cat: missing file operand\nTry: cat <filename>'
      }
    }

    const file = files[filename.toLowerCase()]
    if (file) {
      return {
        type: 'output',
        content: file
      }
    } else {
      return {
        type: 'error',
        content: `cat: ${filename}: No such file or directory`
      }
    }
  },

  neofetch() {
    return {
      type: 'info',
      content: `
                    .-/+oossssoo+/-.               pratham@portfolio
                .:+ssssssssssssssss+:.           ----------------
              -+ssssssssssssssssssssss+-         OS: Portfolio Linux
            -+sssssssssssssssssssssssssss+-      Host: Creative Workstation
          -+sssssssssssssssssssssssssssssss+-    Kernel: 5.15.0-creativity
        .+sssssssssssssssssssssssssssssssssss+.  Uptime: 5 years, 2 months
       +sssssssssssssssssssssssssssssssssssssss+ Shell: portfolio-terminal
      +ssssssssssssssssssssssssssssssssssssssss+ Resolution: ∞x∞
     .ssssssssssssssssssssssssssssssssssssssss.  DE: React Desktop Environment
     .ssssssssssssssssssssssssssssssssssssssss.  WM: JavaScript Window Manager
      +ssssssssssssssssssssssssssssssssssssssss+ Theme: Dark Terminal
       +sssssssssssssssssssssssssssssssssssssss+ Icons: Font Awesome
        .+sssssssssssssssssssssssssssssssssss+.  Terminal: pratham-terminal
          -+sssssssssssssssssssssssssssssss+-    CPU: Creative Brain (8) @ 3.5GHz
            -+sssssssssssssssssssssssssss+-      GPU: Imagination Graphics
              .:+ssssssssssssssssssss+:.        Memory: Unlimited
                  .-/+oossssoo+/-.             
`
    }
  },



  async pokedex(argument) {
    if (!argument) {
      return {
        type: 'info',
        content: `🔍 Pokedex Usage:

pokedex show -<pokemon_name>

Examples:
  pokedex show -pikachu
  pokedex show -charizard
  pokedex show -mewtwo

This will display a 3D model of the Pokemon using the Pokemon 3D API.`
      }
    }

    // Parse the command format: show -<pokemon_name>
    const parts = argument.trim().split(' ')
    if (parts.length !== 2 || parts[0] !== 'show' || !parts[1].startsWith('-')) {
      return {
        type: 'error',
        content: `❌ Invalid format. Use: pokedex show -<pokemon_name>

Example: pokedex show -pikachu`
      }
    }

    const pokemonName = parts[1].substring(1).toLowerCase() // Remove the '-' prefix

    try {
      // First, get basic Pokemon info from PokeAPI
      const pokeResponse = await fetch(`https://pokeapi.co/api/v2/pokemon/${pokemonName}`)

      if (!pokeResponse.ok) {
        return {
          type: 'error',
          content: `❌ Pokemon "${pokemonName}" not found. Please check the spelling and try again.`
        }
      }

      const pokemonData = await pokeResponse.json()

      // Get the 3D model URL using the Pokemon ID from the Pokemon 3D API
      const model3DUrl = `https://raw.githubusercontent.com/Sudhanshu-Ambastha/Pokemon-3D/main/models/glb/regular/${pokemonData.id}.glb`

      // Return the Pokemon 3D viewer component
      return {
        type: 'success',
        isComponent: true,
        content: React.createElement(Pokemon3DViewer, {
          pokemonName: pokemonData.name.charAt(0).toUpperCase() + pokemonData.name.slice(1),
          modelUrl: model3DUrl,
          pokemonData: pokemonData
        })
      }
    } catch (error) {
      return {
        type: 'error',
        content: `❌ Error fetching Pokemon data: ${error.message}

Please check your internet connection and try again.`
      }
    }
  }
}
